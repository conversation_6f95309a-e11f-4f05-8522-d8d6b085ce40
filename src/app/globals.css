@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom fonts from Arknights official website */
@font-face {
  font-family: 'SourceHanSansSC';
  src: url('/fonts/SourceHanSansSC-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansSC';
  src: url('/fonts/SourceHanSansSC-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansSC';
  src: url('/fonts/SourceHanSansSC-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Novecentosanswide';
  src: url('/fonts/Novecentosanswide-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Novecentosanswide';
  src: url('/fonts/Novecentosanswide-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Oswald';
  src: url('/fonts/Oswald-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bender';
  src: url('/fonts/Bender-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bender';
  src: url('/fonts/Bender-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

:root {
  --font-ak-primary: 'Oswald', 'SourceHanSansSC', system-ui, sans-serif;
  --font-ak-secondary: 'Oswald', 'Novecentosanswide', monospace;
  --font-ak-title: 'Bender', 'Novecentosanswide', sans-serif;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background-color: #0a0a0a;
  color: #ffffff;
  font-family: var(--font-ak-primary);
  font-weight: 500; /* 使用 Medium 字重 */
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #0099ff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00ccff;
}

/* 原网站精确CSS类名映射 */

/* layout内容容器 - 对应 _7c7241c0 */
._7c7241c0 {
  position: relative;
  min-height: 100vh;
  background-image: url('/images/backgrounds/bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
  width: 100%;
  height: 100vh;
}

/* 内层容器 - 对应 _dd208b33 */
._dd208b33 {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 主要内容容器 - 对应 _d3fe6857 */
._d3fe6857 {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  padding-top: 120px; /* 为固定导航栏留出空间 */
  overflow: hidden;
  cursor: grab;
}

._d3fe6857:active {
  cursor: grabbing;
}

/* 页面容器 - 对应 _c629adb0 (覆盖滑动模式) */
._c629adb0 {
  position: absolute;
  top: 0;
  left: auto;
  width: 0%;
  height: 100%;
  overflow: hidden;
  will-change: left, width;
  backface-visibility: hidden;
  transition: left 0.6s cubic-bezier(0.25, 0.1, 0.25, 1),
              width 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* 当前激活页面的样式 */
._c629adb0.active {
  left: 0px;
  width: 100%;
}

/* 页面内容容器 */
.ak-section-content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 各个页面的特定样式类名 */
._2a56b767 {
  /* ImprovedIndexSection 首页主要内容 */
  position: relative;
  width: 100%;
  height: 100%;
  left: 0px;
}

._446c7f49 {
  /* ImprovedInformationSection 情报主要内容 */
  position: relative;
  width: 100%;
  height: 100%;
  left: auto;
}

._f7ef99ca {
  /* OperatorSection 干员主要内容 */
  position: relative;
  width: 100%;
  height: 100%;
  left: auto;
}

._f6634292._b9f6df56 {
  /* WorldSection 设定主要内容 */
  position: relative;
  width: 100%;
  height: 100%;
  left: auto;
}

._4629490e {
  /* MediaSection 泰拉万象主要内容 */
  position: relative;
  width: 100%;
  height: 100%;
  left: auto;
}

._d08c6381 {
  /* MoreSection 更多内容 */
  position: relative;
  width: 100%;
  height: 100%;
  left: auto;
}

.ak-section-content {
  transition: none; /* 内容容器不需要额外动画 */
  will-change: auto;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  ._d3fe6857 {
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
  }

  .ak-section-content {
    -webkit-overflow-scrolling: touch;
  }
}

/* 性能优化 */
._d3fe6857,
._c629adb0,
.ak-section-content {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 保持向后兼容 */
.ak-section-wrapper {
  width: 100%;
  height: 100vh;
  position: relative;
}

/* 背景电路板效果 - 对应 _917c2cd7 _616534e8 */
._917c2cd7 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

._616534e8 {
  /* 电路板背景特效样式 */
}

/* 电路板Canvas - 对应 _fb1ee57e */
._fb1ee57e {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  opacity: 0.3;
}

/* 背景粒子效果Canvas - 对应 _c25881b2 */
._c25881b2 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  display: block;
  opacity: 0.5;
}

/* WebGL容器 */
.webgl-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

/* 保持向后兼容 */
.ak-circuit-canvas,
.ak-particle-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* 布局直观分隔线条 - 对应 _60828c90 _1ae85f83 */
._60828c90 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

._1ae85f83 {
  /* 分隔线条容器样式 */
}

/* 分隔线条基础样式 - 对应 _b8e46dd4 */
._b8e46dd4 {
  position: absolute;
  background: rgba(0, 153, 255, 0.08);
  opacity: 0.6;
}

/* 第一条线 - 对应 _cf56609f _0cf06031 */
._cf56609f._0cf06031 {
  top: 0;
  left: 20%;
  width: 1px;
  height: 100%;
}

/* 第二条线 - 对应 _c9ef635e _5f620bec */
._c9ef635e._5f620bec {
  top: 0;
  right: 20%;
  width: 1px;
  height: 100%;
}

/* 第三条线 - 对应 _405e32e2 _5f620bec */
._405e32e2._5f620bec {
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
}

/* 保持向后兼容 */
.ak-layout-lines {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.ak-line {
  position: absolute;
  background: rgba(0, 153, 255, 0.1);
}

.ak-line-1 {
  top: 0;
  left: 20%;
  width: 1px;
  height: 100%;
}

.ak-line-2 {
  top: 0;
  right: 20%;
  width: 1px;
  height: 100%;
}

.ak-line-3 {
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
}

/* 切换主要内容时的指示箭头 - 对应 _10351ad1 _6a4a1a75 */
._10351ad1 {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
  display: flex;
  align-items: center;
  gap: 2rem;
}

._6a4a1a75 {
  /* 指示箭头容器样式 */
}

/* 滚动内容区域 - 对应 _c5888fbe */
._c5888fbe {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 罗德岛Logo - 对应 _fbe74f1b */
._fbe74f1b {
  width: 2rem;
  height: 2rem;
}

._fbe74f1b svg {
  width: 100%;
  height: 100%;
  fill: #0099ff;
}

/* 滚动文本容器 - 对应 _1381d70c */
._1381d70c {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* SCROLL文字 - 对应 _5cbd5f8c */
._5cbd5f8c {
  font-family: var(--font-ak-secondary);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 箭头图标 - 对应 _28a18e28 */
._28a18e28 {
  width: 1rem;
  height: 1rem;
}

._28a18e28 svg {
  width: 100%;
  height: 100%;
  fill: #0099ff;
}

/* 上箭头容器 - 对应 _d710e1d3 */
._d710e1d3 {
  cursor: pointer;
  transition: transform 0.2s ease;
}

._d710e1d3:hover {
  transform: translateY(-2px);
}

/* 右侧边占位内容 - 对应 _ae86e3f9 */
._ae86e3f9 {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 20;
  text-align: right;
  font-family: var(--font-ak-secondary);
}

/* 保持向后兼容 */
.ak-sidebar-content {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 20;
  text-align: right;
  font-family: var(--font-ak-secondary);
}

/* 右侧边栏数字 - 对应 _a6fb5251 */
._a6fb5251 {
  font-size: 4rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1;
  margin-bottom: 0.5rem;
  font-family: var(--font-ak-secondary);
  letter-spacing: 0.1em;
}

/* 右侧边栏分数 - 对应 _0df54c95 */
._0df54c95 {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 1rem;
  font-family: var(--font-ak-secondary);
  font-weight: 400;
}

/* 右侧边栏标题 - 对应 _a229ec44 */
._a229ec44 {
  font-size: 1.25rem;
  font-weight: bold;
  color: #0099ff;
  margin-bottom: 0.5rem;
  font-family: var(--font-ak-secondary);
  letter-spacing: 0.05em;
}

/* 右侧边栏副标题 - 对应 _f46541ee */
._f46541ee {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: var(--font-ak-secondary);
  font-weight: 400;
}

/* ===== 右侧边栏已完全使用 Tailwind CSS 重构 ===== */
/* 所有样式已迁移到组件的 className 中，无需额外 CSS */

/* ===== 新增的设计感样式 ===== */

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-track-transparent {
  scrollbar-color: rgba(0, 153, 255, 0.3) transparent;
}

.scrollbar-thumb-ak-primary\/30::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thumb-ak-primary\/30::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thumb-ak-primary\/30::-webkit-scrollbar-thumb {
  background: rgba(0, 153, 255, 0.3);
  border-radius: 3px;
}

.scrollbar-thumb-ak-primary\/30::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 153, 255, 0.5);
}

/* 网格背景动画 */
.bg-grid-pattern {
  animation: gridFlow 4s ease-in-out infinite;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .pl-0.pr-64 {
    padding-left: 0;
    padding-right: 16rem;
  }
}

@media (max-width: 768px) {
  .pl-0.pr-64 {
    padding-left: 0;
    padding-right: 12rem;
  }
}





/* 保持向后兼容 */
.ak-sidebar-number {
  font-size: 4rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.ak-sidebar-fraction {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 1rem;
}

.ak-sidebar-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #0099ff;
  margin-bottom: 0.5rem;
}

.ak-sidebar-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

/* 滚动指示器 */
.ak-scroll-indicator {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.ak-scroll-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ak-rhodes-logo {
  width: 2rem;
  height: 2rem;
}

.ak-rhodes-logo svg {
  width: 100%;
  height: 100%;
  fill: #0099ff;
}

.ak-scroll-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ak-scroll-label {
  font-family: var(--font-ak-secondary);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.ak-scroll-arrow {
  width: 1rem;
  height: 1rem;
}

.ak-scroll-arrow svg {
  width: 100%;
  height: 100%;
  fill: #0099ff;
}

.ak-scroll-up {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.ak-scroll-up:hover {
  transform: translateY(-2px);
}

/* OriginalNavigation组件容器 - 对应 _6066ead1 */
._6066ead1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  z-index: 30;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 153, 255, 0.1);
}

/* Logo样式 - 对应 _6532021f */
._6532021f {
  display: block;
  width: 120px;
  height: auto;
  transition: transform 0.2s ease;
}

._6532021f:hover {
  transform: scale(1.05);
}

._6532021f svg {
  width: 100%;
  height: auto;
  fill: #0099ff;
}

/* 导航容器 - 对应 _a5b206bf */
._a5b206bf {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 2rem;
}

/* 导航内容容器 - 对应 _57987e85 */
._57987e85 {
  display: flex;
  align-items: center;
  gap: 2rem;
}

/* 导航链接 - 对应 _c91e47cc */
._c91e47cc {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  position: relative;
}

._c91e47cc:hover {
  background: rgba(0, 153, 255, 0.1);
  transform: translateY(-2px);
}

/* 激活状态 - 对应 _f3a31881 */
._c91e47cc._f3a31881 {
  background: rgba(0, 153, 255, 0.15);
}

._c91e47cc._f3a31881::after {
  content: '';
  position: absolute;
  bottom: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 2px;
  background: #0099ff;
}

/* 导航英文标题 - 对应 _70ad2676 */
._70ad2676 {
  font-family: var(--font-ak-secondary);
  font-size: 0.875rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

._c91e47cc._f3a31881 ._70ad2676 {
  color: #0099ff;
}

/* 导航中文标题 - 对应 _ae0d96a6 */
._ae0d96a6 {
  font-family: var(--font-ak-primary);
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

._c91e47cc._f3a31881 ._ae0d96a6 {
  color: rgba(0, 153, 255, 0.8);
}

/* 导航右边icon区域 - 对应 _149a6e98 */
._149a6e98 {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 图标按钮 - 对应 _950ab946 */
._950ab946 {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 153, 255, 0.2);
}

._950ab946:hover {
  background: rgba(0, 153, 255, 0.1);
  border-color: rgba(0, 153, 255, 0.4);
  transform: translateY(-2px);
}

._950ab946 svg {
  width: 1.25rem;
  height: 1.25rem;
  fill: rgba(255, 255, 255, 0.8);
  transition: fill 0.3s ease;
}

._950ab946:hover svg {
  fill: #0099ff;
}

/* 音频图标特殊样式 - 对应 _e6c9defd _5d27adee */
._950ab946._e6c9defd._5d27adee {
  background: rgba(0, 153, 255, 0.1);
  border-color: rgba(0, 153, 255, 0.3);
}

._950ab946._e6c9defd._5d27adee svg {
  fill: #0099ff;
}

/* 装饰线条容器 - 对应 _d6b1e15c */
._d6b1e15c {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  overflow: hidden;
}

/* 装饰线条内容 - 对应 _a174eeef */
._a174eeef {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 装饰线条基础样式 - 对应 _016b1d43 */
._016b1d43 {
  position: absolute;
  height: 1px;
  background: rgba(0, 153, 255, 0.3);
  animation: decorativeLine 3s ease-in-out infinite;
}

/* 第一条装饰线 - 对应 _a1600518 */
._016b1d43._a1600518 {
  left: 0;
  width: 30%;
  animation-delay: 0s;
}

/* 第二条装饰线 - 对应 _b0d45f10 */
._016b1d43._b0d45f10 {
  left: 35%;
  width: 25%;
  animation-delay: 1s;
}

/* 第三条装饰线 - 对应 _daaf28c6 */
._016b1d43._daaf28c6 {
  right: 0;
  width: 20%;
  animation-delay: 2s;
}

@keyframes decorativeLine {
  0%, 100% {
    opacity: 0.3;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleX(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  ._6066ead1 {
    padding: 1rem 1.5rem;
  }

  ._57987e85 {
    gap: 1.5rem;
  }

  ._6532021f {
    width: 100px;
  }

  ._70ad2676 {
    font-size: 0.8rem;
  }

  ._ae0d96a6 {
    font-size: 0.7rem;
  }
}

@media (max-width: 768px) {
  ._6066ead1 {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
    height: auto;
  }

  ._a5b206bf {
    order: 3;
    width: 100%;
    margin: 1rem 0 0 0;
  }

  ._57987e85 {
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  ._c91e47cc {
    padding: 0.25rem 0.5rem;
  }

  ._70ad2676 {
    font-size: 0.75rem;
  }

  ._ae0d96a6 {
    font-size: 0.65rem;
  }

  ._149a6e98 {
    gap: 0.75rem;
  }

  ._950ab946 {
    width: 2rem;
    height: 2rem;
  }

  ._950ab946 svg {
    width: 1rem;
    height: 1rem;
  }
}

@media (max-width: 480px) {
  ._6066ead1 {
    padding: 0.5rem;
  }

  ._6532021f {
    width: 80px;
  }

  ._57987e85 {
    gap: 0.5rem;
  }

  ._c91e47cc {
    padding: 0.25rem;
  }

  ._70ad2676 {
    font-size: 0.7rem;
  }

  ._ae0d96a6 {
    font-size: 0.6rem;
  }
}

/* 点击导航上icon_social时弹出的内容层 - 对应 _5a5107d2 _7f5ebf8d */
._5a5107d2 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  z-index: 100;
  display: none;
  align-items: center;
  justify-content: center;
}

._7f5ebf8d {
  /* 弹出层通用样式 */
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* 点击导航上icon_user时弹出的内容层 - 对应 _6975b23b _7f5ebf8d */
._6975b23b {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  z-index: 100;
  display: none;
  align-items: center;
  justify-content: center;
}

/* 保持向后兼容 */
.ak-social-popup,
.ak-user-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 100;
  display: none;
  align-items: center;
  justify-content: center;
}

/* LoadingScreen组件样式 - 对应原网站 _369c736f _8f5b35e5 */
.ak-loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #0a0a0a;
  z-index: 50;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ak-loading-logo {
  margin-bottom: 4rem;
}

.ak-logo-svg {
  text-align: center;
}

.ak-loading-bottom {
  position: absolute;
  bottom: 4rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  width: 100%;
  max-width: 600px;
}

.ak-copyright-mini {
  margin-bottom: 2rem;
}

.ak-loading-progress {
  width: 100%;
}

.ak-progress-container {
  position: relative;
  width: 100%;
  height: 2px;
  margin-bottom: 1rem;
}

.ak-progress-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
}

.ak-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #0099ff;
  transition: width 0.1s ease;
}

.ak-loading-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ak-loading-status {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-ak-secondary);
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.ak-double-arrow {
  color: #0099ff;
  margin-right: 0.5rem;
  font-weight: bold;
}

.ak-loading-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-family: var(--font-ak-secondary);
  font-size: 0.75rem;
}

.ak-brand {
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

.ak-url {
  color: rgba(255, 255, 255, 0.6);
}

/* Loading animations */
.loading-dots {
  display: inline-block;
  animation: loading-dots-blink 1.5s infinite;
}

@keyframes loading-dots-blink {
  0%, 20% {
    opacity: 0.3;
  }
  40% {
    opacity: 0.6;
  }
  60% {
    opacity: 0.9;
  }
  80%, 100% {
    opacity: 1;
  }
}

/* Glitch effect */
.glitch {
  position: relative;
  color: #ffffff;
  font-size: 2rem;
  font-weight: bold;
  text-transform: uppercase;
  animation: glitch-skew 1s infinite linear alternate-reverse;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-anim 2s infinite linear alternate-reverse;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-anim2 3s infinite linear alternate-reverse;
  color: #0000ff;
  z-index: -2;
}

@keyframes glitch-anim {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-3px, 3px);
  }
  40% {
    transform: translate(-3px, -3px);
  }
  60% {
    transform: translate(3px, 3px);
  }
  80% {
    transform: translate(3px, -3px);
  }
  to {
    transform: translate(0);
  }
}

@keyframes glitch-anim2 {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(2px, -2px);
  }
  40% {
    transform: translate(-2px, 2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(-2px, -2px);
  }
  to {
    transform: translate(0);
  }
}

@keyframes glitch-skew {
  0% {
    transform: skew(0deg);
  }
  20% {
    transform: skew(5deg);
  }
  40% {
    transform: skew(-5deg);
  }
  60% {
    transform: skew(3deg);
  }
  80% {
    transform: skew(-3deg);
  }
  to {
    transform: skew(0deg);
  }
}

/* Original Arknights website CSS classes */
._7c7241c0 {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-image: url('/images/backgrounds/bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

._dd208b33 {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

._d3fe6857 {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

._c629adb0 {
  position: absolute;
  top: 0;
  height: 100%;
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
  overflow: hidden;
}

/* Index Section - 对应 _2a56b767 */
._2a56b767 {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Information Section - 对应 _446c7f49 */
._446c7f49 {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
}

/* Operator Section - 对应 _f7ef99ca */
._f7ef99ca {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* World Section - 对应 _f6634292 */
._f6634292 {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

/* Media Section - 对应 _4629490e */
._4629490e {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* More Section - 对应 _d08c6381 */
._d08c6381 {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
}

/* Background effects */
._917c2cd7._616534e8 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

._fb1ee57e {
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.webgl-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

._c25881b2 {
  width: 100%;
  height: 100%;
  opacity: 0.5;
}

/* Layout divider lines */
._60828c90._1ae85f83 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

._b8e46dd4 {
  position: absolute;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
}

._cf56609f._0cf06031 {
  top: 20%;
  left: 0;
  width: 100%;
  height: 1px;
}

._c9ef635e._5f620bec {
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
}

._405e32e2._5f620bec {
  top: 80%;
  left: 0;
  width: 100%;
  height: 1px;
}

/* Navigation arrows */
._10351ad1._6a4a1a75 {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

._c5888fbe {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

._fbe74f1b {
  width: 2rem;
  height: 2rem;
  opacity: 0.7;
}

._1381d70c {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

._5cbd5f8c {
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.1em;
  opacity: 0.8;
}

._28a18e28 {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

._28a18e28:hover {
  color: #ff6b35;
  transform: scale(1.1);
}

._d710e1d3 {
  cursor: pointer;
  transition: all 0.3s ease;
}

._d710e1d3:hover {
  transform: scale(1.1);
}

/* Right side content */
._ae86e3f9 {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  color: white;
}

._a6fb5251 {
  font-family: 'Novecentosanswide', sans-serif;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
  opacity: 0.9;
}

._0df54c95 {
  font-family: 'Novecentosanswide', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.7;
  letter-spacing: 0.1em;
}

._a229ec44 {
  font-family: 'Novecentosanswide', sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: 0.2em;
  margin-top: 1rem;
  opacity: 0.9;
}

._f46541ee {
  font-family: 'Novecentosanswide', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.15em;
  opacity: 0.7;
}

/* Navigation */
._6066ead1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

/* Popup layers */
._5a5107d2._7f5ebf8d,
._6975b23b._7f5ebf8d {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  display: none;
}

/* Section content styling */
.ak-section-content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Responsive design */
@media (max-width: 768px) {
  ._ae86e3f9 {
    right: 1rem;
  }

  ._10351ad1._6a4a1a75 {
    bottom: 1rem;
    left: 1rem;
  }

  ._a6fb5251 {
    font-size: 2rem;
  }
}/* Imp
rovedIndexSection specific styles */
._2a56b767._47e76c6c {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

._165feda9 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
  z-index: 1;
}

._daa0ae89 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

._02a74b8e {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  opacity: 0.3;
}

._96034668,
._120363a9,
._311266e9 {
  position: absolute;
  background: radial-gradient(circle, rgba(255,107,53,0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
}

._96034668 {
  top: 10%;
  right: 20%;
  width: 200px;
  height: 200px;
}

._120363a9 {
  bottom: 20%;
  left: 10%;
  width: 150px;
  height: 150px;
}

._311266e9 {
  top: 60%;
  right: 40%;
  width: 100px;
  height: 100px;
}

/* Main content area */
._db42efb9 {
  position: absolute;
  top: 50%;
  left: 5%;
  transform: translateY(-50%);
  z-index: 10;
  color: white;
}

._f030b7bc {
  margin-bottom: 2rem;
}

._eb6721a0 {
  font-family: 'Novecentosanswide', sans-serif;
  font-size: 4rem;
  font-weight: 700;
  letter-spacing: 0.2em;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

._a8defe45 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

._a4cb003f {
  font-family: 'Novecentosanswide', sans-serif;
  font-size: 1.5rem;
  font-weight: 500;
  letter-spacing: 0.15em;
  opacity: 0.9;
}

._d0bb52f6 {
  font-family: 'Oswald', sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  opacity: 0.7;
}

._bb7846aa {
  width: 166px;
  height: 18px;
  opacity: 0.6;
}

/* Download links */
._e32dd037 {
  position: absolute;
  top: 50%;
  right: 5%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  z-index: 10;
  max-width: 220px;
}

._46f42502 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(0,0,0,0.6);
  border: 1px solid rgba(255,255,255,0.15);
  border-radius: 6px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 180px;
  font-size: 0.875rem;
}

._46f42502:hover {
  background: rgba(255,107,53,0.3);
  border-color: rgba(255,107,53,0.6);
  transform: translateX(-3px);
}

._46f42502._f2a0009b {
  background: rgba(0,122,255,0.15);
  border-color: rgba(0,122,255,0.25);
}

._46f42502._f2a0009b:hover {
  background: rgba(0,122,255,0.3);
  border-color: rgba(0,122,255,0.5);
}

._46f42502._79edd75a {
  background: rgba(76,175,80,0.15);
  border-color: rgba(76,175,80,0.25);
}

._46f42502._79edd75a:hover {
  background: rgba(76,175,80,0.3);
  border-color: rgba(76,175,80,0.5);
}

._46f42502._c9c4dad8 {
  background: rgba(255,193,7,0.15);
  border-color: rgba(255,193,7,0.25);
}

._46f42502._c9c4dad8:hover {
  background: rgba(255,193,7,0.3);
  border-color: rgba(255,193,7,0.5);
}

._46f42502._1287fb1f {
  background: rgba(156,39,176,0.15);
  border-color: rgba(156,39,176,0.25);
}

._46f42502._1287fb1f:hover {
  background: rgba(156,39,176,0.3);
  border-color: rgba(156,39,176,0.5);
}

._46f42502._fa2889b4 {
  background: rgba(33,150,243,0.15);
  border-color: rgba(33,150,243,0.25);
}

._46f42502._fa2889b4:hover {
  background: rgba(33,150,243,0.3);
  border-color: rgba(33,150,243,0.5);
}

._46f42502._969794e2 {
  background: rgba(255,152,0,0.15);
  border-color: rgba(255,152,0,0.25);
}

._46f42502._969794e2:hover {
  background: rgba(255,152,0,0.3);
  border-color: rgba(255,152,0,0.5);
}

._86418c91 {
  width: 1.75rem;
  height: 1.75rem;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

._86418c91 svg,
._86418c91 img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

._523e0079 {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

._2b80a3ba {
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.2;
}

._c6a344ce {
  font-size: 0.875rem;
  opacity: 0.8;
  line-height: 1.2;
}

/* QR Code and age rating */
._d16ac76f {
  position: absolute;
  bottom: 4%;
  right: 5%;
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  z-index: 10;
}

._fbc56429 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(0,0,0,0.4);
  padding: 0.75rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
}

._a77441b7 {
  display: flex;
  flex-direction: column;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.1em;
  opacity: 0.9;
  line-height: 1.2;
}

._3d5286a4 {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  background: white;
  padding: 3px;
  object-fit: contain;
}

._1661c959 {
  width: 48px;
  height: auto;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  border-radius: 4px;
}

._1661c959:hover {
  opacity: 1;
}

/* Responsive design for ImprovedIndexSection */
@media (max-width: 1024px) {
  ._eb6721a0 {
    font-size: 3rem;
  }

  ._e32dd037 {
    right: 2%;
  }

  ._db42efb9 {
    left: 2%;
  }
}

@media (max-width: 768px) {
  ._eb6721a0 {
    font-size: 2rem;
  }

  ._a4cb003f {
    font-size: 1.25rem;
  }

  ._e32dd037 {
    position: static;
    transform: none;
    margin-top: 2rem;
  }

  ._db42efb9 {
    position: static;
    transform: none;
    padding: 2rem;
  }

  ._d16ac76f {
    bottom: 2%;
    right: 2%;
  }

  ._46f42502 {
    min-width: 180px;
    padding: 0.75rem 1rem;
  }
}
/* ImprovedInformationSection 样式 - 基于UI图精确重构 */
._446c7f49 {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #0f0f0f 50%, #0a0a0a 100%);
  overflow: hidden;
}

._446c7f49._1d83b18b {
  /* 当前激活状态的样式 */
}

._d034f64c {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse 800px 400px at 20% 30%, rgba(0, 153, 255, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse 600px 300px at 80% 70%, rgba(0, 204, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

._4c6251ec {
  position: relative;
  width: 100%;
  height: 500px;
  margin-top: 80px;
  overflow: hidden;
  padding: 0 80px;
}

._34ed7167 {
  width: 100%;
  height: 100%;
  position: relative;
}

.swiper-wrapper {
  display: flex;
  transition-timing-function: ease-out;
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition-property: transform;
  box-sizing: content-box;
}

.swiper-slide {
  flex-shrink: 0;
  height: 100%;
  position: relative;
  transition-property: transform;
  border-radius: 12px;
  overflow: hidden;
}

._c684c97a {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
}

._c493fa1c {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

._c684c97a:hover {
  transform: translateY(-8px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(0, 153, 255, 0.3),
    0 0 30px rgba(0, 153, 255, 0.2);
}

._c684c97a:hover ._c493fa1c {
  transform: scale(1.05);
}

._84945841 {
  position: relative;
  width: calc(100% - 160px);
  height: 4px;
  margin: 40px auto 0;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 2px;
  overflow: hidden;
}

._fcf0101c {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 2px;
}

.swiper-scrollbar-drag {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #0099ff 0%, #00ccff 50%, #0099ff 100%);
  border-radius: 2px;
  transition: all 0.4s ease;
  box-shadow:
    0 0 15px rgba(0, 153, 255, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.swiper-scrollbar-drag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

._2474bf68 {
  position: absolute;
  top: 12%;
  right: 6%;
  width: 400px;
  height: 400px;
  border: 1px solid rgba(0, 153, 255, 0.15);
  border-radius: 50%;
  pointer-events: none;
  animation: rotate-slow 150s linear infinite;
}

._2474bf68::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  border: 1px solid rgba(0, 204, 255, 0.08);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

._5debcceb {
  position: absolute;
  bottom: 8%;
  left: 3%;
  width: 250px;
  height: 250px;
  border: 1px solid rgba(0, 204, 255, 0.12);
  border-radius: 50%;
  pointer-events: none;
  animation: rotate-slow 100s linear infinite reverse;
}

._5debcceb::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  border: 1px solid rgba(0, 153, 255, 0.06);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

._f00913a8 {
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
  padding: 80px 60px;
  z-index: 10;
}

._f00913a8 ._6c630746 {
  position: absolute;
  left: 60px;
  top: 50%;
  transform: translateY(-50%);
}

._1aa87c3a {
  position: absolute;
  top: 0;
  left: 0;
  width: 120px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #0099ff, transparent);
}

._7e5f4af0 {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00ccff, transparent);
}

._6c630746 {
  max-width: 500px;
  margin: 0;
}

._1ec422e3 {
  width: 18px;
  height: 18px;
  color: #0099ff;
  transition: transform 0.3s ease;
}

._78dcf6c5 {
  font-family: 'Oswald', monospace;
  font-size: 14px;
  color: #00ccff;
  margin-bottom: 6px;
  letter-spacing: 1px;
  font-weight: 500;
}

._8419bfdd {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding-bottom: 15px;
  flex-wrap: wrap;
}

._5adb7306 {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 0;
  font-size: 16px;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

._5adb7306:hover {
  color: #0099ff;
}

._5adb7306._1d83b18b {
  color: #0099ff;
}

._5adb7306._1d83b18b::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #0099ff, #00ccff);
  border-radius: 1px;
}

._5adb7306 ._1ec422e3 {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
}

._5adb7306:hover ._1ec422e3 {
  transform: rotate(90deg);
}

._0882dfb6 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

._b6f1b9e5 {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

._b6f1b9e5::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: transparent;
  transition: background 0.3s ease;
}

._b6f1b9e5:hover::before {
  background: linear-gradient(180deg, #0099ff, #00ccff);
}

._b6f1b9e5:hover {
  background: rgba(0, 153, 255, 0.1);
  border-color: rgba(0, 153, 255, 0.3);
  transform: translateX(5px);
}

._b34281ac {
  padding: 4px 12px;
  background: #0099ff;
  color: #000;
  font-size: 11px;
  font-weight: 600;
  border-radius: 12px;
  min-width: 50px;
  text-align: center;
  flex-shrink: 0;
}

._4e2f6712 {
  flex: 1;
}

._9c298aed {
  font-size: 14px;
  color: #ffffff;
  margin-top: 4px;
  line-height: 1.4;
  font-weight: 400;
}

._b9c239f0 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 24px;
  border: 2px solid rgba(0, 153, 255, 0.2);
  border-radius: 8px;
  color: #0099ff;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

._b9c239f0::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 153, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

._b9c239f0:hover::before {
  left: 100%;
}

._b9c239f0:hover {
  background: rgba(0, 153, 255, 0.1);
  border-color: #0099ff;
  box-shadow: 0 4px 20px rgba(0, 153, 255, 0.2);
}

/* 动画效果 */
@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 153, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 153, 255, 0.6);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  ._f00913a8 {
    padding: 60px 40px;
  }

  ._f00913a8 ._6c630746 {
    left: 40px;
  }

  ._6c630746 {
    max-width: 450px;
  }
}

@media (max-width: 768px) {
  ._f00913a8 {
    padding: 40px 20px;
  }

  ._f00913a8 ._6c630746 {
    position: static;
    transform: none;
    max-width: 100%;
  }

  ._8419bfdd {
    gap: 20px;
  }

  ._4c6251ec {
    height: 300px;
    margin-top: 40px;
    padding: 0 20px;
  }
}/* Imp
rovedInformationSection 额外优化样式 */

/* 轮播图片的特殊效果 */
._c684c97a {
  position: relative;
  overflow: hidden;
}

._c684c97a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 153, 255, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  pointer-events: none;
}

._c684c97a:hover::before {
  opacity: 1;
}

/* 新闻列表项的增强效果 */
._b6f1b9e5 {
  position: relative;
  overflow: hidden;
}

._b6f1b9e5::after {
  content: '';
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 153, 255, 0.1), transparent);
  transition: right 0.5s ease;
}

._b6f1b9e5:hover::after {
  right: 100%;
}

/* 筛选器的激活状态增强 */
._5adb7306._1d83b18b {
  background: rgba(0, 153, 255, 0.1);
  border-radius: 4px;
  padding: 12px 16px;
  margin: -12px -16px;
}



/* 滚动条的发光效果 */
.swiper-scrollbar-drag {
  box-shadow: 0 0 10px rgba(0, 153, 255, 0.4);
}

/* 装饰元素的脉冲效果 */
._2474bf68,
._5debcceb {
  animation: pulse-border 4s ease-in-out infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgba(0, 153, 255, 0.1);
    transform: scale(1);
  }
  50% {
    border-color: rgba(0, 153, 255, 0.3);
    transform: scale(1.05);
  }
}

/* 提升整体视觉层次 */
._f00913a8 {
  position: relative;
  z-index: 2;
}

._4c6251ec {
  position: relative;
  z-index: 1;
}

/* 优化文字可读性 */
._3f4d7cff,
._bd9048e0 {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

._9c298aed {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
