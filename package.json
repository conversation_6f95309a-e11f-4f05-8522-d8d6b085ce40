{"name": "arknights-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@pixi/react": "^8.0.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@splinetool/react-spline": "^4.1.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "framer-motion": "^11.11.17", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "simplex-noise": "^4.0.3", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "vaul": "^1.1.2", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^20.17.9", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/three": "^0.178.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "15.4.3", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}